'use client';

import { useState, useEffect } from 'react';
import {
  Container,
  Title,
  Text,
  Stack,
  Paper,
  TextInput,
  Textarea,
  Button,
  Group,
  Alert,
  Divider,
  NumberInput
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconMail, IconUser, IconMessage, IconCheck, IconX, IconMath } from '@tabler/icons-react';
import { AdSenseBanner } from 'src/components/AdSense';
import FullLayout from 'src/components/layouts/FullLayout';

export default function ContactUsPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [loading, setLoading] = useState(false);
  const [mathQuestion, setMathQuestion] = useState({ num1: 0, num2: 0, answer: 0 });
  const [userAnswer, setUserAnswer] = useState<number | string>('');

  // Generate new math question
  const generateMathQuestion = () => {
    const num1 = Math.floor(Math.random() * 9) + 1; // 1-9
    const num2 = Math.floor(Math.random() * 9) + 1; // 1-9
    const answer = num1 + num2;
    setMathQuestion({ num1, num2, answer });
    setUserAnswer('');
  };

  // Generate initial math question
  useEffect(() => {
    generateMathQuestion();
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.name.trim() || !formData.email.trim() || !formData.message.trim()) {
      notifications.show({
        title: 'Validation Error',
        message: 'Please fill in all required fields',
        color: 'red',
        icon: <IconX size={16} />
      });
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      notifications.show({
        title: 'Invalid Email',
        message: 'Please enter a valid email address',
        color: 'red',
        icon: <IconX size={16} />
      });
      return;
    }

    // Math captcha validation
    if (Number(userAnswer) !== mathQuestion.answer) {
      notifications.show({
        title: 'Incorrect Answer',
        message: 'Please solve the math problem correctly to verify you are not a robot',
        color: 'red',
        icon: <IconX size={16} />
      });
      generateMathQuestion(); // Generate new question
      return;
    }

    setLoading(true);
    
    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          mathAnswer: userAnswer,
          expectedAnswer: mathQuestion.answer
        }),
      });

      if (response.ok) {
        notifications.show({
          title: 'Message Sent!',
          message: 'Thank you for contacting us. We will get back to you soon.',
          color: 'green',
          icon: <IconCheck size={16} />
        });
        
        // Reset form
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: ''
        });
        generateMathQuestion(); // Generate new question after successful submission
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to send message. Please try again later.',
        color: 'red',
        icon: <IconX size={16} />
      });
    } finally {
      setLoading(false);
    }
  };

  return (
  <FullLayout>
        <Stack gap="lg">
          <div style={{ textAlign: 'center' }}>
            <Title order={1} mb="sm">
              Contact Us..
            </Title>
            <Text size="lg" c="dimmed">
              We'd love to hear from you. Send us a message and we'll respond as soon as possible.
            </Text>
               <AdSenseBanner slot="2099365759" responsive={false} width={468} height={60} />
          </div>

          <Divider />

          <Alert color="blue" variant="light">
            <Text size="sm">
              <strong>About ODude Names:</strong> ODude Names are decentralized names that give you full control. 
              No one can block or control your name, and the service is lifetime free. 
              Your personal details are securely stored and only displayed as you choose.
            </Text>
          </Alert>

          <form onSubmit={handleSubmit}>
            <Stack gap="md">
              <Group grow>
                <TextInput
                  label="Name"
                  placeholder="Your full name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  leftSection={<IconUser size={16} />}
                  required
                  disabled={loading}
                />
                <TextInput
                  label="Email"
                  placeholder="<EMAIL>"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  leftSection={<IconMail size={16} />}
                  required
                  disabled={loading}
                />
              </Group>

              <TextInput
                label="Subject"
                placeholder="What is this regarding?"
                value={formData.subject}
                onChange={(e) => handleInputChange('subject', e.target.value)}
                leftSection={<IconMessage size={16} />}
                disabled={loading}
              />

              <Textarea
                label="Message"
                placeholder="Tell us more about your inquiry..."
                value={formData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                minRows={5}
                maxRows={10}
                required
                disabled={loading}
              />

              {/* Math Captcha */}
              <Alert color="blue" variant="light">
                <Text size="sm" mb="xs">
                  <strong>Anti-Spam Verification:</strong> Please solve this simple math problem to verify you are not a robot.
                </Text>
                <Group align="center" gap="sm">
                  <Text size="lg" fw={500}>
                    {mathQuestion.num1} + {mathQuestion.num2} =
                  </Text>
                  <NumberInput
                    placeholder="?"
                    value={userAnswer}
                    onChange={setUserAnswer}
                    min={0}
                    max={18}
                    size="sm"
                    style={{ width: 80 }}
                    leftSection={<IconMath size={16} />}
                    required
                    disabled={loading}
                  />
                  <Button
                    variant="light"
                    size="xs"
                    onClick={generateMathQuestion}
                    disabled={loading}
                  >
                    New Question
                  </Button>
                </Group>
              </Alert>

              <Group justify="center" mt="lg">
                <Button
                  type="submit"
                  size="md"
                  loading={loading}
                  leftSection={<IconMail size={16} />}
                >
                  Send Message
                </Button>
              </Group>
            </Stack>
          </form>

          <Divider />

          <div style={{ textAlign: 'center' }}>
            <Text size="sm" c="dimmed">
              You can also reach us directly at our support email for urgent <NAME_EMAIL>
            </Text>
          </div>
        </Stack>
      </FullLayout>
  );
}
