import { NextResponse } from 'next/server';
import { Client as FTPClient } from 'basic-ftp';
import { FTP_HOST, FTP_USER, FTP_PASSWORD, FTP_PATH, ADMIN_EMAIL } from 'src/lib/config';
import { auth } from 'auth';

/**
 * Get last backup information from FTP server
 */
async function getLastBackupInfo(): Promise<{ lastBackupTime?: Date; message?: string }> {
  const client = new FTPClient();

  try {
    // Connect to FTP server
    await client.access({
      host: FTP_HOST,
      user: FTP_USER,
      password: FTP_PASSWORD,
      secure: false
    });

    // Ensure backup directory exists
    try {
      await client.ensureDir(FTP_PATH);
    } catch (error) {
      // Directory might not exist, continue
    }

    // List files in backup directory
    const fileList = await client.list(FTP_PATH);

    if (fileList.length === 0) {
      return { message: 'No backups found' };
    }

    // Find backup files - look for any CSV files that contain table names or backup patterns
    const backupFiles = fileList.filter(file =>
      file.name.endsWith('.csv') && (
        file.name.includes('backup_') ||
        file.name.includes('contact_') ||
        file.name.includes('bookmark_') ||
        file.name.includes('profiles_')
      )
    );

    if (backupFiles.length === 0) {
      return { message: 'No backup files found' };
    }

    // Get the most recent file modification time
    const mostRecentFile = backupFiles.reduce((latest, current) =>
      current.modifiedAt && latest.modifiedAt && current.modifiedAt > latest.modifiedAt ? current : latest
    );

    if (!mostRecentFile.modifiedAt) {
      return { message: 'Cannot determine last backup time' };
    }

    return { 
      lastBackupTime: mostRecentFile.modifiedAt,
      message: 'Last backup found'
    };

  } catch (error) {
    console.error('Error getting last backup info:', error);
    return { message: 'Error checking backup status' };
  } finally {
    client.close();
  }
}

export async function GET() {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session?.user?.email || session.user.email !== ADMIN_EMAIL) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const backupInfo = await getLastBackupInfo();

    return NextResponse.json({
      lastBackupTime: backupInfo.lastBackupTime?.toISOString() || null,
      message: backupInfo.message,
      hasBackup: !!backupInfo.lastBackupTime
    });

  } catch (error) {
    console.error('Backup status API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
